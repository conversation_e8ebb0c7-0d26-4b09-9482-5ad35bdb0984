<script setup lang="ts">
import type { IMission } from '~/api/misc'

interface Props {
  mission: IMission
}

interface QuestionAnswer {
  choice: string
  info: string
}

interface QuestionData {
  q: string
  answer: QuestionAnswer[]
  bingo: string
}

interface MissionData {
  title: string
  question: QuestionData[]
  intro: string
  info: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  complete: [missionId: string]
}>()

// Parse mission data
const missionData = computed<MissionData>(() => {
  try {
    return typeof props.mission.missionInfo === 'string'
      ? JSON.parse(props.mission.missionInfo)
      : props.mission.missionInfo
  }
  catch {
    return { title: '', question: [], intro: '', info: '' }
  }
})

const isFinish = computed(() => props.mission?.isFinished ?? false)
const currentQuestionIndex = ref(0)
const selectedAnswer = ref<string>('')
const isAnswering = ref(false)
const showResult = ref(false)
const isCorrect = ref(false)

const currentQuestion = computed(() => {
  return missionData.value.question?.[currentQuestionIndex.value]
})

const validAnswers = computed(() => {
  return currentQuestion.value?.answer?.filter(a => a.choice && a.info) || []
})

const correctAnswer = computed(() => {
  const bingo = currentQuestion.value?.bingo || ''
  const match = bingo.match(/^([A-Z])\s?(.*)$/)
  return match ? { choice: match[1], explanation: match[2]?.replace(/^[.\s]+/, '') } : null
})

const selectAnswer = (choice: string) => {
  if (isAnswering.value || showResult.value)
    return
  selectedAnswer.value = choice
}

const submitAnswer = async () => {
  if (!selectedAnswer.value || isAnswering.value)
    return

  // Simulate API call delay
  await sleep(800)

  const correct = selectedAnswer.value === correctAnswer.value?.choice
  isCorrect.value = correct
  showResult.value = false
  isAnswering.value = false

  console.log('当前任务 ID {}', currentQuestionIndex.value)
  if (currentQuestionIndex.value < missionData.value.question?.length - 1) {
    await sleep(200)
    currentQuestionIndex.value++
    // showResult.value = true
    selectedAnswer.value = ''
    isAnswering.value = false
    isCorrect.value = false
    return
  }

  console.log('提交')
  // Success - call API and emit complete after showing result
  emit('complete', '99999')
  // emit('complete', props.mission?.missionId ?? '')
  // }
}

const resetQuestion = () => {
  selectedAnswer.value = ''
  showResult.value = false
  isCorrect.value = false
}

// 立即完成任务
const _completeImmediately = () => {
  emit('complete', props.mission?.missionId ?? '')
}
</script>

<template>
  <div class="question-mission min-h-full">
    <!-- Header -->
    <div class="p-4">
      <div class="flex items-center gap-3 mb-4">
        <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
          <div class="i-material-symbols-quiz text-white text-xl" />
        </div>
        <div>
          <h2 class="text-base font-bold text-gray-900 dark:text-white">
            任务类型
          </h2>
          <p class="text-xs text-gray-600 dark:text-gray-400">
            {{ missionData.intro }}
          </p>
          <span class="text-10px text-gray-600 dark:text-gray-400">第 {{ currentQuestionIndex + 1 }} 题 / 共 {{ missionData.question?.length || 0 }} 题</span>
        </div>
      </div>

      <div class="mb">
        <!-- Mission Title -->
        <div class="bg-white dark:bg-gray-800 rounded-xl py-4 px-2 shadow-sm border border-gray-200 dark:border-gray-700">
          <div class="flex items-start gap-3">
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center shrink-0">
              <div class="i-material-symbols-lightbulb text-yellow-500 text-xl shrink-0" />
            </div>
            <p class="text-gray-700 dark:text-gray-300 leading-relaxed text-pretty">
              {{ missionData.title }}
            </p>
          </div>
        </div>
      </div>

      <div class="">
        <!-- Task Instructions -->
        <div class="bg-white dark:bg-gray-800 rounded-xl py-4 px-2 shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
          <div class="flex items-start gap-3">
            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center shrink-0">
              <div class="i-material-symbols-task-alt text-purple-600 dark:text-purple-400 text-lg" />
            </div>
            <div class="flex-1">
              <h3 class="font-semibold text-gray-900 dark:text-white mb-2">
                互动说明
              </h3>
              <p class="text-gray-700 dark:text-gray-300 leading-relaxed text-pretty">
                {{ missionData.info }}
              </p>
            </div>
          </div>
        </div>

        <!-- Question Content -->
        <div v-if="currentQuestion" class="pb-6">
          <!-- Question -->
          <div class="bg-white dark:bg-gray-800 rounded-xl py-4 px-2 shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
            <div class="flex items-start gap-3">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center shrink-0">
                <div class="i-material-symbols-help text-blue-600 dark:text-blue-400 text-lg shrink-0" />
              </div>
              <div class="flex-1">
                <p class="text-gray-700 dark:text-gray-300 leading-relaxed text-pretty">
                  {{ currentQuestion.q }}
                </p>
              </div>
            </div>
          </div>

          <!-- Answers -->
          <div class="space-y-3 mb-6">
            <h4 class="font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <div class="i-material-symbols-checklist text-blue-500" />
              请选择答案
            </h4>

            <div class="space-y-3">
              <div
                v-for="answer in validAnswers"
                :key="answer.choice"
                class="answer-option"
                :class="{
                  selected: selectedAnswer === answer.choice,
                  correct: showResult && answer.choice === correctAnswer?.choice,
                  incorrect: showResult && selectedAnswer === answer.choice && !isCorrect,
                  disabled: isAnswering || showResult,
                }"
                @click="selectAnswer(answer.choice)"
              >
                <div class="flex items-center gap-4">
                  <div class="choice-indicator">
                    <div
                      v-if="showResult && answer.choice === correctAnswer?.choice"
                      class="i-material-symbols-check text-white text-sm"
                    />
                    <div
                      v-else-if="showResult && selectedAnswer === answer.choice && !isCorrect"
                      class="i-material-symbols-close text-white text-sm"
                    />
                    <span v-else class="choice-letter">{{ answer.choice }}</span>
                  </div>
                  <div class="flex-1">
                    <p class="text-gray-700 dark:text-gray-300 leading-relaxed text-pretty">
                      {{ answer.info }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-center">
            <button
              v-if="!showResult"
              :disabled="!selectedAnswer || isAnswering || isFinish"
              class="submit-btn outline-none border-none"
              @click="submitAnswer"
            >
              <div v-if="isAnswering" class="i-svg-spinners-ring-resize text-lg mr-2" />
              <div v-else class="i-material-symbols-send text-lg mr-2" />
              <span v-if="!isFinish">
                {{ isAnswering ? '提交中...' : '提交答案' }}
              </span>
              <span v-else>
                任务已完成，感谢您的参与！
              </span>
            </button>
            <button v-else-if="!isCorrect" class="retry-btn outline-none border-none w-full" @click="resetQuestion">
              <div class="i-material-symbols-refresh text-lg mr-2" />
              重新作答
            </button>
          </div>
        </div>

        <!-- No Question State -->
        <!--        <div v-else class="flex flex-col items-center justify-center py-20 px-6"> -->
        <!--          <div class="i-material-symbols-quiz text-4xl text-gray-400 mb-4" /> -->
        <!--          <p class="text-gray-600 dark:text-gray-400 text-center"> -->
        <!--            暂无题目数据 -->
        <!--          </p> -->
        <!--          &lt;!&ndash; 立即完成按钮 - 仅在 missionId 为 4 时显示 &ndash;&gt; -->
        <!--          <div v-if="props.mission.missionId === '4'" class="flex justify-center mt-4"> -->
        <!--            <button -->
        <!--              class="complete-btn outline-none border-none" -->
        <!--              @click="completeImmediately" -->
        <!--            > -->
        <!--              <div class="i-material-symbols-check-circle text-lg mr-2" /> -->
        <!--              立即完成 -->
        <!--            </button> -->
        <!--          </div> -->
        <!--        </div> -->
      </div>
    </div>
  </div>
</template>

<style scoped>
.answer-option {
  @apply bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-700
         rounded-xl p-2 cursor-pointer transition-all duration-200
         hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-sm;
}

.answer-option.selected {
  @apply border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20;
}

.answer-option.correct {
  @apply border-green-500 dark:border-green-400 bg-green-50 dark:bg-green-900/20;
}

.answer-option.incorrect {
  @apply border-red-500 dark:border-red-400 bg-red-50 dark:bg-red-900/20;
}

.answer-option.disabled {
  @apply cursor-not-allowed opacity-75;
}

.choice-indicator {
  @apply w-8 h-8 rounded-full border-2 border-gray-300 dark:border-gray-600
         flex items-center justify-center flex-shrink-0 transition-all duration-200;
}

.selected .choice-indicator {
  @apply border-blue-500 dark:border-blue-400 bg-blue-500 dark:bg-blue-400;
}

.correct .choice-indicator {
  @apply border-green-500 dark:border-green-400 bg-green-500 dark:bg-green-400;
}

.incorrect .choice-indicator {
  @apply border-red-500 dark:border-red-400 bg-red-500 dark:bg-red-400;
}

.choice-letter {
  @apply text-sm font-semibold text-gray-600 dark:text-gray-400;
}

.selected .choice-letter,
.correct .choice-letter,
.incorrect .choice-letter {
  @apply text-white;
}

.submit-btn {
  @apply px-6 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400
         text-white font-medium rounded-xl shadow-lg hover:shadow-xl
         transition-all duration-200 transform hover:scale-105 active:scale-95
         disabled:cursor-not-allowed disabled:hover:scale-100 disabled:shadow-lg
         flex items-center justify-center min-w-32 w-full;
}

.success-message {
  @apply text-center text-green-600 dark:text-green-400
         bg-green-50 dark:bg-green-900/20 rounded-xl px-6 py-4
         border border-green-200 dark:border-green-800;
}

.retry-btn {
  @apply px-6 py-2 bg-orange-500 hover:bg-orange-600 text-white font-medium
         rounded-xl shadow-lg hover:shadow-xl transition-all duration-200
         transform hover:scale-105 active:scale-95 flex items-center justify-center;
}

.complete-btn {
  @apply px-6 py-2 bg-green-500 hover:bg-green-600 text-white font-medium
         rounded-xl shadow-lg hover:shadow-xl transition-all duration-200
         transform hover:scale-105 active:scale-95 flex items-center justify-center
         min-w-32;
}
</style>
