<script setup lang="ts">
import type { IMission } from '~/api/misc'
import * as WX from 'jswx'

interface Props {
  mission: IMission
}

interface ScanData {
  title: string
  question: string
  intro: string
  info: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  complete: [missionId: string]
}>()

// Parse mission data
const missionData = computed<ScanData>(() => {
  try {
    return typeof props.mission.missionInfo === 'string'
      ? JSON.parse(props.mission.missionInfo)
      : props.mission.missionInfo
  }
  catch {
    return { title: '', question: '', intro: '', info: '' }
  }
})

const isScanning = ref(false)
const isCompleted = ref(false)
const scanResult = ref<string>('')
const isFinish = computed(() => props.mission?.isFinished ?? false)

const startScan = () => {
  WX.scanQRCode({
    success: (res: any) => {
      console.log('Scan result:', res)
    },
    fail: (res: any) => {
      console.log('Scan failed:', res)
      uni.showToast({
        title: '扫码失败',
        icon: 'none',
      })
    },
  })
}

const resetScan = () => {
  scanResult.value = ''
  isCompleted.value = false
}

// 跳过扫码，直接完成任务
const skipScan = async () => {
  try {
    uni.showToast({
      title: '任务已完成！',
      icon: 'success',
    })

    isCompleted.value = true

    // Complete mission after short delay
    await sleep(1500)
    emit('complete', props.mission?.missionId ?? '')
  }
  catch (error) {
    console.error('Skip scan failed:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'error',
    })
  }
}
</script>

<template>
  <div class="scan-mission min-h-full">
    <!-- Header -->
    <div class="p-2">
      <div class="flex items-center gap-3 mb-4">
        <div class="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
          <div class="i-material-symbols-qr-code-scanner text-white text-xl shrink-0" />
        </div>
        <div>
          <h2 class="text-sm font-bold text-gray-900 dark:text-white">
            任务类型
          </h2>
          <p class="text-gray-600 dark:text-gray-400">
            {{ missionData.intro }}
          </p>
        </div>
      </div>

      <!-- Mission Title -->
      <div class="bg-white dark:bg-gray-800 rounded-xl py-4 px-2 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-start gap-3">
          <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center shrink-0">
            <div class="i-material-symbols-explore text-orange-500 text-lg shrink-0" />
          </div>
          <p class="text-gray-700 dark:text-gray-300 leading-relaxed text-pretty">
            {{ missionData.title }}
          </p>
        </div>
      </div>
    </div>

    <!-- Scan Content -->
    <div class="p-2">
      <!-- Task Instructions -->
      <div class="bg-white dark:bg-gray-800 rounded-xl py-4 px-2 shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
        <div class="flex items-start gap-3">
          <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center shrink-0">
            <div class="i-material-symbols-task-alt text-orange-600 dark:text-orange-400 text-lg" />
          </div>
          <div class="flex-1">
            <h3 class="font-semibold text-gray-900 dark:text-white mb-2">
              互动说明
            </h3>
            <p class="text-gray-700 dark:text-gray-300 leading-relaxed text-pretty">
              {{ missionData.info }}
            </p>
          </div>
        </div>
      </div>

      <!-- Scan Status Area -->
      <div class="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
        <h4 class="font-semibold text-gray-900 dark:text-white  flex items-center gap-2">
          <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center shrink-0">
            <div class="i-material-symbols-qr-code text-orange-500 text-lg shrink-0" />
          </div>
          <span v-if="!isFinish">
            {{ scanResult ? '扫码结果' : isScanning ? '正在扫码...' : '等待扫码' }}
          </span>
          <span v-else>
            任务已完成，感谢您的参与！
          </span>
        </h4>

        <div v-if="scanResult || isCompleted" class="scan-status-area mt-3" :class="[scanResult || isCompleted ? 'min-h-48' : '']">
          <!-- Scan Result -->
          <div v-if="scanResult" class="relative">
            <div class="scan-result-card">
              <div class="flex items-center gap-3 mb-3">
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                  <div class="i-material-symbols-check-circle text-green-600 dark:text-green-400 text-xl" />
                </div>
                <div>
                  <h5 class="font-semibold text-gray-900 dark:text-white">
                    扫码成功
                  </h5>
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    已识别二维码内容
                  </p>
                </div>
              </div>

              <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                <p class="text-sm text-gray-700 dark:text-gray-300 font-mono break-all">
                  {{ scanResult }}
                </p>
              </div>
            </div>

            <div v-if="isCompleted" class="absolute inset-0 bg-green-500/20 rounded-lg flex items-center justify-center">
              <div class="bg-green-500 text-white px-4 py-2 rounded-full flex items-center gap-2">
                <div class="i-material-symbols-check-circle text-lg" />
                <span class="font-medium">任务完成！</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div v-if="!isFinish" class="flex flex-col gap-3">
        <!-- Start Scan Button -->
        <button
          v-if="!scanResult"
          :disabled="isScanning"
          class="scan-btn bg-orange-500 hover:bg-orange-600"
          @click="startScan"
        >
          <div v-if="isScanning" class="i-svg-spinners-ring-resize text-xl mr-2" />
          <div v-else class="i-material-symbols-qr-code-scanner text-xl mr-2" />
          {{ isScanning ? '扫码中...' : '开始扫码' }}
        </button>

        <!-- Skip Scan Button -->
        <button
          v-if="!scanResult && !isScanning"
          class="scan-btn-skip bg-blue-500 hover:bg-blue-600"
          @click="skipScan"
        >
          <div class="i-material-symbols-skip-next text-xl mr-2" />
          跳过扫码
        </button>

        <!-- Scan Actions -->
        <div v-else-if="!isCompleted" class="flex gap-3">
          <button
            class="scan-btn-secondary flex-1"
            @click="resetScan"
          >
            <div class="i-material-symbols-refresh text-lg mr-2" />
            重新扫码
          </button>
        </div>

        <!-- Success State -->
        <div v-else class="success-message">
          <div class="i-material-symbols-celebration text-2xl mb-2" />
          <p class="font-semibold mb-1 text-sm">
            扫码任务完成！
          </p>
          <p class="opacity-80 text-12px">
            即将进入下一关...
          </p>
        </div>
      </div>
    </div>

    <!-- No Data State -->
    <div v-if="!missionData.title && !missionData.question" class="flex flex-col items-center justify-center py-20 px-6">
      <div class="i-material-symbols-qr-code-scanner text-4xl text-gray-400 mb-4" />
      <p class="text-gray-600 dark:text-gray-400 text-center">
        暂无扫码任务数据
      </p>
    </div>
  </div>
</template>

<style scoped>
/* Component Styles */
.scan-status-area {
  @apply flex items-center justify-center;
}

.scan-placeholder {
  @apply w-full h-48 bg-gray-50 dark:bg-gray-700 border-2 border-dashed
         border-gray-300 dark:border-gray-600 rounded-lg
         flex flex-col items-center justify-center relative;
}

.scan-loading-state {
  @apply w-full h-48 flex flex-col items-center justify-center;
}

.scan-result-card {
  @apply w-full bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800
         rounded-lg p-4 relative overflow-hidden;
}

.qr-code-icon {
  @apply relative;
}

.scan-frame {
  @apply absolute inset-0 w-16 h-16 -mt-2 mx-auto;
}

.corner {
  @apply absolute w-3 h-3 border-2;
}

.corner-tl {
  @apply top-0 left-0 border-r-0 border-b-0;
}

.corner-tr {
  @apply top-0 right-0 border-l-0 border-b-0;
}

.corner-bl {
  @apply bottom-0 left-0 border-r-0 border-t-0;
}

.corner-br {
  @apply bottom-0 right-0 border-l-0 border-t-0;
}

.scan-btn {
  @apply w-full py-1 px-6 text-white font-medium rounded-xl shadow-lg
         transition-all duration-200 transform hover:scale-105 active:scale-95
         disabled:cursor-not-allowed disabled:hover:scale-100 disabled:opacity-50
         flex items-center justify-center outline-none border-none relative overflow-hidden;
}

.scan-btn:hover::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.scan-btn-secondary {
  @apply w-full py-1 px-6 bg-gray-500 hover:bg-gray-600 text-white font-medium rounded-xl shadow-lg
         transition-all duration-200 transform hover:scale-105 active:scale-95
         flex items-center justify-center outline-none border-none relative overflow-hidden;
}

.scan-btn-secondary:hover::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.scan-btn-skip {
  @apply w-full py-1 px-6 text-white font-medium rounded-xl shadow-lg
         transition-all duration-200 transform hover:scale-105 active:scale-95
         flex items-center justify-center outline-none border-none relative overflow-hidden;
}

.scan-btn-skip:hover::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.success-message {
  @apply text-center text-green-600 dark:text-green-400
         bg-green-50 dark:bg-green-900/20 rounded-xl px-6 py-4
         border border-green-200 dark:border-green-800;
}

.bg-orange-500 {
  position: relative;
  overflow: hidden;
}

.bg-orange-500::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

/* Focus states */
.scan-btn:focus,
.scan-btn-secondary:focus,
.scan-btn-skip:focus {
  outline: 2px solid #fb923c;
  outline-offset: 2px;
}
</style>
