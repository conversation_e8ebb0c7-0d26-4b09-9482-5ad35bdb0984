<script setup lang="ts">
import { useModalEvents, useSideEffects } from '@bryce-loskie/use'
import { domToBlob } from 'modern-screenshot'
import { motion } from 'motion-v'
import { photoFrameImage0Url, photoFrameImage1Url, photoFrameImage2Url } from '~/logic/preload-assets'
import { isShareRulesModalVisibleRef, isTakePhotoModalVisibleRef } from '~/logic/task'
import { showConfirm } from '../TheModal'

const emit = defineEmits(['completeTakePhoto'])

const photoFrames = [
  { id: 0, url: photoFrameImage0Url, title: '相框01' },
  { id: 1, url: photoFrameImage1Url, title: '相框02' },
  { id: 2, url: photoFrameImage2Url, title: '相框03' },
]

const imageUrlRef = ref<string>()
const generatedImageUrlRef = ref<string>()

const takePhoto = () => {
  emit('completeTakePhoto')
  uni.chooseImage({
    count: 1,
    sizeType: ['original', 'compressed'],
    sourceType: ['camera'],
    success(res) {
      console.log('success', res)
      const tempFilePaths = res.tempFilePaths
      if (tempFilePaths && tempFilePaths.length > 0) {
        imageUrlRef.value = tempFilePaths[0]
        uni.showToast({
          title: '拍照成功',
          icon: 'success',
        })
      }
    },
    fail(err) {
      console.error('拍照失败:', err)
      let errorMsg = '拍照失败'

      // 根据不同错误类型给出不同提示
      if (err.errMsg) {
        if (err.errMsg.includes('cancel')) {
          errorMsg = '用户取消拍照'
        }
        else if (err.errMsg.includes('permission')) {
          errorMsg = '请授权相机权限'
        }
        else if (err.errMsg.includes('system')) {
          errorMsg = '系统错误，请重试'
        }
      }

      uni.showToast({
        title: errorMsg,
        icon: 'none',
      })
    },
  })
}

const swiperRef = ref<UniHelper.SwiperInstance>()
const swiperHeightRef = ref<number>(0)

watchEffect(() => {
  const elem = unrefElement(swiperRef)
  if (elem) {
    swiperHeightRef.value = elem.clientHeight
  }
})

const { onClose, onOpen } = useModalEvents(isTakePhotoModalVisibleRef)

onOpen(() => {
  uni.showToast({
    title: '恭喜您获得探险家相框，您可左右滑动选择您喜欢的相框',
    icon: 'none',
    duration: 3_000,
  })
})

onClose(() => {
  imageUrlRef.value = undefined
})

const containerRef = ref<HTMLDivElement>()
const generatedImageFileRef = shallowRef<File>()

onClose(() => {
  generatedImageFileRef.value = undefined
  generatedImageUrlRef.value = undefined
})

const generateImageFromContainer = async () => {
  const elem = unrefElement(containerRef)
  if (!elem) {
    console.log('elem', elem)
    return
  }

  const blob = await domToBlob(elem, {
    font: false,
  })

  if (!blob) {
    console.log('blob', blob)
    return
  }

  const uid = Date.now()
  const image = new File([blob], `image_${uid}.png`, { type: 'image/png' })
  generatedImageFileRef.value = image
  return image
}

const sideEffects = useSideEffects()

onClose(() => {
  sideEffects.dispose()
})

/**
 * 1. generate image from containerRef element
 * 2. upload image to server
 */
const handleUploadImage = async () => {
  uni.showLoading({ title: '生成图片中...' })
  const [generateImageErr, image] = await to(generateImageFromContainer())
  uni.hideLoading()
  if (generateImageErr || !image) {
    console.log('generateImageErr', generateImageErr)
    console.log('image', image)
    uni.showToast({
      title: '生成图片失败',
      icon: 'none',
    })
    return
  }

  uni.showToast({ title: '上传图片中...', icon: 'none' })
  const [err, res] = await to(uploadFile({
    file: image,
    onProgress(progressData) {
      uni.showToast({
        title: `上传进度: ${progressData.percent.toFixed(0)}%`,
        icon: 'none',
      })
    },
  }))
  const url = res?.url
  if (err || !url) {
    uni.showToast({ title: '上传失败, 请稍后重试', icon: 'none' })
    return
  }
  uni.hideToast()
  // uni.showToast({
  //   title: '上传成功, 海报已生成',
  //   icon: 'success',
  // })

  miscApi.shareSocial()

  const img = new Image()
  img.src = url
  img.onload = () => {
    generatedImageUrlRef.value = url
  }
}

/**
 * 你已经获得了7个能量石
 * 快去线下兑换好礼
 * 现在分享话题至社交媒体赢更多好礼
 */
const handleNext = () => {
  isTakePhotoModalVisibleRef.value = false

  const { close } = showConfirm({
    title: '提示',
    message: `
你已经获得了全部 7 颗能量石
快去线下兑换好礼
现在分享话题至社交媒体赢更多好礼
    `.trim(),
    confirmText: '复制分享',
    cancelText: '活动规则',
    showCancel: true,
    onConfirm: () => {
      // TODO: set clipboard data as rules
      uni.setClipboardData({
        data: `今日份快乐是“极地嗨浪探险家”给的！😎
在天津极地馆变身探险家，闯关成功，专属相框Get！
#天津极地嗨浪节# 坐等抽大奖！`,
      }).then(() => {
        uni.showToast({
          title: '复制成功, 快去分享吧',
          icon: 'success',
        })
      })
      close()
    },
    onCancel: () => {
      // 打开活动规则页面
      isShareRulesModalVisibleRef.value = true
      close()
    },
  })
}
</script>

<template>
  <TheModal
    v-model:visible="isTakePhotoModalVisibleRef"
    width="100vw"
    gap="0"
    :closeable="!generatedImageUrlRef"
  >
    <template v-if="generatedImageUrlRef">
      <div class="w-full h-110 flex flex-col items-center relative">
        <motion.img
          :src="generatedImageUrlRef"
          class="w-full h-full object-cover rounded-xl overflow-hidden"
          :initial="{ rotate: 0 }"
          :while-in-view="{ rotate: 6 }"
          :transition="{ delay: 0.3, type: 'spring' }"
        />
      </div>

      <!-- Beautiful instruction UI for long press to save -->
      <div class="w-full px-6 py-4 flex flex-col items-center gap-3">
        <!-- Animated instruction card -->
        <motion.div
          class="bg-gradient-to-br from-blue-50 to-purple-50 border border-blue-200/50 rounded-2xl p-4 w-full max-w-sm shadow-lg backdrop-blur-sm"
          :initial="{ opacity: 0, y: 20 }"
          :animate="{ opacity: 1, y: 0 }"
          :transition="{ duration: 0.5, delay: 0.2 }"
        >
          <div class="flex items-center justify-center gap-3 mb-2">
            <!-- Animated hand icon -->
            <motion.div
              class="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full shadow-lg"
              :animate="{ scale: [1, 1.1, 1] }"
              :transition="{ duration: 2, repeat: Infinity }"
            >
              <span class="i-tabler:hand-finger text-white text-lg" />
            </motion.div>

            <!-- Pulsing dots -->
            <div class="flex gap-1">
              <motion.div
                v-for="i in 3"
                :key="i"
                class="w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full"
                :animate="{ opacity: [0.3, 1, 0.3] }"
                :transition="{ duration: 1.5, repeat: Infinity, delay: i * 0.2 }"
              />
            </div>

            <!-- Download icon -->
            <motion.div
              class="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full shadow-lg"
              :animate="{ rotate: [0, 10, -10, 0] }"
              :transition="{ duration: 2, repeat: Infinity, delay: 1 }"
            >
              <span class="i-tabler:download text-white text-lg" />
            </motion.div>
          </div>

          <!-- Instruction text -->
          <div class="text-center">
            <p class="text-gray-700 font-medium text-sm leading-relaxed">
              长按海报图片保存到相册
            </p>
            <p class="text-gray-500 text-xs mt-1">
              Long press the poster to save
            </p>
          </div>
        </motion.div>

        <!-- Additional tip with sparkle animation -->
        <motion.div
          class="flex items-center gap-2 text-white text-xs op80"
          :initial="{ opacity: 0 }"
          :animate="{ opacity: 1 }"
          :transition="{ duration: 0.5, delay: 0.8 }"
        >
          <motion.span
            class="i-tabler:sparkles text-yellow-500"
            :animate="{ rotate: [0, 180, 360] }"
            :transition="{ duration: 3, repeat: Infinity }"
          />
          <span>轻松保存您的专属海报</span>
        </motion.div>

        <!-- 我已保存，下一步 -->
        <motion.button
          class="bg-gradient-to-tr from-blue-500 text-white to-purple-600 rounded-full w-full h-12 flex items-center justify-center shadow-lg shadow-blue-500/30 border-2 border-white/20"
          :while-hover="{ scale: 1.1 }"
          :while-press="{ scale: 0.9 }"
          @click="handleNext"
        >
          我已保存，下一步
        </motion.button>
      </div>
    </template>

    <template v-else>
      <div ref="containerRef" class="w-full h-110 flex flex-col items-center relative">
        <div
          v-if="imageUrlRef"
          class="absolute inset-x-[calc(10vw+20px)] z-1 top-1/2 -translate-y-48%"
          :style="{ height: `${swiperHeightRef * 0.8}px` }"
        >
          <img
            :src="imageUrlRef"
            class="w-full h-full object-cover rounded-xl overflow-hidden"
          >
        </div>

        <swiper
          ref="swiperRef"
          class="w-full h-full relative z-2"
          :autoplay="false"
          :circular="true"
          :duration="300"
        >
          <swiper-item
            v-for="frame in photoFrames"
            :key="frame.id"
            class="flex justify-center items-center"
          >
            <div class="w-full h-auto aspect-141/166 flex justify-center items-center">
              <img
                :src="frame.url"
                :alt="frame.title"
                class="w-full h-full"
                draggable="false"
              >
            </div>
          </swiper-item>
        </swiper>
      </div>

      <div class="w-full mb flex justify-center items-center relative z-2 translate-y--1 gap-4">
        <motion.button
          class="
         bg-gradient-to-tr from-blue-500 to-purple-600
         rounded-full
         w-18 h-12
         flex items-center justify-center
         shadow-lg shadow-blue-500/30
         border-2 border-white/20
       "
          :while-hover="{ scale: 1.1 }"
          :while-press="{ scale: 0.9 }"
          @click="takePhoto"
        >
          <span class="i-tabler:camera-bolt text-white text-lg" />
          <p v-if="imageUrlRef" class="font-hand text-white text-stroke-button">
            重拍
          </p>
          <p v-else class="font-hand text-white text-stroke-button">
            拍照
          </p>
        </motion.button>

        <!-- upload image -->
        <motion.button
          v-if="imageUrlRef"
          class="
         bg-gradient-to-tr from-blue-500 to-purple-600
         rounded-full
         w-18 h-12
         flex items-center justify-center
         shadow-lg shadow-blue-500/30
         border-2 border-white/20
       "
          :while-hover="{ scale: 1.1 }"
          :while-press="{ scale: 0.9 }"
          @click="handleUploadImage"
        >
          <span class="i-tabler:cloud-upload text-white text-lg" />
          <p class="font-hand text-white text-stroke-button">
            保存
          </p>
        </motion.button>
      </div>
    </template>
  </TheModal>
</template>
