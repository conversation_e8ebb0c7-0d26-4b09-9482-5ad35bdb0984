<script setup lang="ts">
import { isShareRulesModalVisibleRef } from '~/logic/task'

const isLoading = ref(false)

const handleClose = () => {
  isShareRulesModalVisibleRef.value = false
}
</script>

<template>
  <wd-popup
    v-model="isShareRulesModalVisibleRef"
    safe-area-inset-bottom
    closable
    position="bottom"
    custom-class="rounded-t-32rpx"
    @close="handleClose"
  >
    <!-- Loading State -->
    <div v-if="isLoading" class="flex flex-col items-center justify-center py-20 px-6">
      <div class="i-svg-spinners-ring-resize text-3xl text-blue-500 mb-4" />
      <p class="text-gray-600 dark:text-gray-400 text-sm">
        加载规则中...
      </p>
    </div>

    <div class="p-4 h-80vh overflow-auto rules-container scrollbar">
      <div class="text-pretty prose" style="font-size: 11px">
        <p style="font-size: 14px;">
          <b>内容挑战赛活动规则</b>
        </p>
        一、总则 <br>
        1.1 参与条件 <br>
        新入园游客须添加天津极地海洋度假区或酒店前台扫码添加官方企业微信，获取电子任务卡及探险地图。 <br>
        以前添加官方企业微信客服的游客，可发送关键词“探险”，获得电子任务卡。 <br>
        二、内容挑战赛规则 <br>
        2.1 内容形式<br>
        支持图文、视频两种分享形式，图文需包含活动现场实拍图，视频时长不低于 30 秒且记录活动参与片段。<br>
        2.2 指定话题<br>
        发布时须添加固定话题 #天津极地嗨浪节，并 @官方账号，（小红书：@海合安天津极地海洋度假区，抖音：@海合安天津极地海洋度假区）<br>
        2.3 排名评判标准及周期<br>
        每自然周根据单条内容点赞量、评论量及发布时间进行排名，前 3 名且点赞量不低于 100 个可获 “北塘海鲜” 毛绒玩具盲盒<br>
        <table>
          <thead>
            <tr>
              <th>奖品名称</th>
              <th style="width: 40%">
                奖品图片
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style="font-size: 11px">
                “北塘海鲜”30CM螃蟹
              </td>
              <td><img src="https://tjlg.haiheangroup.com/gift/7.png" alt=""></td>
            </tr>
            <tr>
              <td style="font-size: 11px">
                “北塘海鲜”27CM龙虾
              </td>
              <td><img src="https://tjlg.haiheangroup.com/gift/8.png" alt=""></td>
            </tr>
            <tr>
              <td style="font-size: 11px">
                “北塘海鲜”18CM章鱼
              </td>
              <td><img src="https://tjlg.haiheangroup.com/gift/9.png" alt=""></td>
            </tr>
          </tbody>
        </table>
        2.4 奖品信息收集<br>
        获奖用户需在获奖名单公示后 48 小时内联系官方客服提供有效信息用于奖品发放，逾期视为放弃；奖品仅限本人签收，不可折现、更换，邮寄范围仅限中国大陆地区。<br>
        详见《天津极地海洋度假区 2025 年 “龙宫宝藏打卡之旅” 社交分享活动规则》<br>
        三、免责条款<br>
        3.1 因不可抗力（自然灾害、政策调整等）或系统故障导致活动无法正常开展，主办方有权暂停或终止活动，不承担赔偿责任。<br>
        3.2 游客在活动期间应遵守园区《游客须知》及相关法律法规，因个人过错、违法行为导致的人身财产损失，由游客自行承担。<br>
        3.3 如发现游客存在作弊、欺诈等违规行为，主办方有权取消其获奖资格，并保留追究法律责任的权利。<br>
        四、附则<br>
        4.1 违规处理<br>
        参与者若存在刷赞、盗用他人内容等作弊行为，一经核实，主办方将取消其获奖资格、追回已发奖品，并保留追究法律责任的权利。<br>
        4.2 法律声明<br>
        分享内容需符合《网络安全法》等法规，不得含侵权、低俗或违法信息。因内容违规导致主办方损失的，参与者需承担全部责任并赔偿损失。<br>
        4.3 规则变更<br>
        主办方可根据实际情况调整活动规则，调整内容将通过园区公告、官方企微及公众号公示，公示后用户继续参与即视为接受新规则。<br>
        4.4 咨询方式<br>
        客服热线：022-66227777（每日 9:00-16:30）；或通过企业微信 “天津极地海洋度假区小助手” 咨询。
      </div>
    </div>
  </wd-popup>
</template>

<style scoped>
/* Enhance readability of HTML content */
.rules-content :deep(h1),
.rules-content :deep(h2),
.rules-content :deep(h3),
.rules-content :deep(h4),
.rules-content :deep(h5),
.rules-content :deep(h6) {
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  line-height: 1.4;
}

.rules-content :deep(h1) {
  font-size: 1.5rem;
  border-bottom: 2px solid rgb(59 130 246);
  padding-bottom: 0.5rem;
}

.rules-content :deep(h2) {
  font-size: 1.25rem;
  color: rgb(59 130 246);
}

.rules-content :deep(p) {
  margin-bottom: 1rem;
  line-height: 1.7;
}

.rules-content :deep(ul),
.rules-content :deep(ol) {
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.rules-content :deep(li) {
  margin: 0.5rem 0;
  line-height: 1.6;
}

.rules-content :deep(strong),
.rules-content :deep(b) {
  font-weight: 600;
  color: rgb(17 24 39);
}

.dark .rules-content :deep(strong),
.dark .rules-content :deep(b) {
  color: rgb(249 250 251);
}

.rules-content :deep(em),
.rules-content :deep(i) {
  font-style: italic;
  color: rgb(75 85 99);
}

.dark .rules-content :deep(em),
.dark .rules-content :deep(i) {
  color: rgb(156 163 175);
}

.rules-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
  border: 1px solid rgb(229 231 235);
  border-radius: 0.5rem;
  overflow: hidden;
}

.dark .rules-content :deep(table) {
  border-color: rgb(75 85 99);
}

.rules-content :deep(th),
.rules-content :deep(td) {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid rgb(229 231 235);
}

.dark .rules-content :deep(th),
.dark .rules-content :deep(td) {
  border-bottom-color: rgb(75 85 99);
}

.rules-content :deep(th) {
  background-color: rgb(249 250 251);
  font-weight: 600;
}

.dark .rules-content :deep(th) {
  background-color: rgb(55 65 81);
}
</style>
