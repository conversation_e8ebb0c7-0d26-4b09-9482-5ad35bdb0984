<script lang="ts" setup>
interface Props {
  title?: string
  message?: string
  type?: 'success' | 'error'
  confirmText?: string
  onConfirm?: () => void
  // 取消按钮相关属性
  showCancel?: boolean
  cancelText?: string
  onCancel?: () => void
  // 超链接相关属性
  linkText?: string
  linkUrl?: string
  onLinkClick?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  message: '',
  confirmText: '确定',
  onConfirm: () => {},
  showCancel: false,
  cancelText: '取消',
  onCancel: () => {},
  linkText: '',
  linkUrl: '',
  onLinkClick: () => {},
})
</script>

<template>
  <div class="message-modal-container">
    <!-- 主体内容区域 -->
    <div class="message-modal-content">
      <!-- 标题区域 -->
      <div v-if="props.title" class="modal-title-container">
        <h2 class="modal-title">
          {{ props.title }}
        </h2>
      </div>

      <!-- 内容区域 -->
      <div class="modal-body">
        <!-- 成功图标 -->
        <div v-if="props.type === 'success'" class="success-icon-container">
          <div class="success-icon">
            <div class="checkmark">
              <div class="checkmark-circle" />
            </div>
          </div>
        </div>

        <!-- 错误图标 -->
        <div v-if="props.type === 'error'" class="error-icon-container">
          <div class="error-icon">
            <div class="error-mark">
              <div class="error-circle" />
            </div>
          </div>
        </div>

        <!-- 消息内容 -->
        <p v-if="props.message" class="modal-message font-hand" style="white-space: pre-line;">
          {{ props.message }}
        </p>

        <!-- 自定义组件 -->
        <slot />

        <!-- 超链接 -->
        <div v-if="props.linkText && props.linkUrl" class="modal-link-container">
          <a
            href="javascript:void(0)"
            class="modal-link"
            @click="props.onLinkClick"
          >
            {{ props.linkText }}
          </a>
        </div>
      </div>

      <!-- 按钮区域 -->
      <div class="modal-footer">
        <!-- 取消按钮 -->
        <ButtonBlue
          v-if="props.showCancel"
          class="modal-cancel-btn"
          @click="props.onCancel"
        >
          {{ props.cancelText || '取消' }}
        </ButtonBlue>
        <!-- 确认按钮 -->
        <ButtonBlue
          class="modal-confirm-btn"
          @click="props.onConfirm"
        >
          {{ props.confirmText || '确定' }}
        </ButtonBlue>
      </div>
    </div>

    <!-- 背景装饰元素 -->
    <div class="modal-bg-decoration">
      <!-- 主要装饰圆圈 -->
      <div class="decoration-circle decoration-circle-1" />
      <div class="decoration-circle decoration-circle-2" />
      <div class="decoration-circle decoration-circle-3" />
      <div class="decoration-circle decoration-circle-4" />

      <!-- 波纹效果 -->
      <div class="decoration-wave decoration-wave-1" />
      <div class="decoration-wave decoration-wave-2" />
      <div class="decoration-wave decoration-wave-3" />

      <!-- 几何图形 -->
      <div class="decoration-triangle decoration-triangle-1" />
      <div class="decoration-triangle decoration-triangle-2" />
      <div class="decoration-square decoration-square-1" />
      <div class="decoration-square decoration-square-2" />

      <!-- 星星装饰 -->
      <div class="decoration-star decoration-star-1">
        ✦
      </div>
      <div class="decoration-star decoration-star-2">
        ✧
      </div>
      <div class="decoration-star decoration-star-3">
        ✦
      </div>
      <div class="decoration-star decoration-star-4">
        ✧
      </div>
      <div class="decoration-star decoration-star-5">
        ✦
      </div>

      <!-- 点阵装饰 -->
      <div class="decoration-dots decoration-dots-1">
        <div class="dot" />
        <div class="dot" />
        <div class="dot" />
        <div class="dot" />
        <div class="dot" />
        <div class="dot" />
      </div>
      <div class="decoration-dots decoration-dots-2">
        <div class="dot" />
        <div class="dot" />
        <div class="dot" />
      </div>

      <!-- 流光效果 -->
      <div class="decoration-glow decoration-glow-1" />
      <div class="decoration-glow decoration-glow-2" />

      <!-- 网格背景 -->
      <div class="decoration-grid" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.message-modal-container {
  width: 100%;
  max-width: 80vw;
  margin: 0 auto;
  position: relative;
  min-height: 60vh;
  will-change: transform, opacity;
}

.message-modal-content {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 48rpx;
  overflow: hidden;
  background:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 70%, rgba(78, 166, 248, 0.3) 0%, transparent 50%),
    linear-gradient(135deg, #3e8af5 0%, #4ea6f8 50%, #5bb3fc 100%);
  box-shadow:
    0 40rpx 80rpx rgba(62, 138, 245, 0.3),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.2);
  padding: 8vw;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 60vh;
  will-change: transform, opacity;
  transform: translateZ(0); /* 启用硬件加速 */
}

.modal-title-container {
  text-align: center;
  padding-bottom: 32rpx;
  animation: textFadeIn 0.6s ease-out 0.2s both;
}

.modal-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  text-align: center;
}

.success-icon-container {
  margin-bottom: 48rpx;
  animation: iconFadeIn 0.6s ease-out 0.2s both;
}

.success-icon {
  width: 128rpx;
  height: 128rpx;
  margin: 0 auto;
}

.checkmark {
  position: relative;
  width: 100%;
  height: 100%;
}

.checkmark-circle {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  border: 6rpx solid white;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(10rpx);
  box-shadow:
    0 8rpx 32rpx rgba(255, 255, 255, 0.1),
    inset 0 2rpx 8rpx rgba(255, 255, 255, 0.2);
}

.checkmark-circle::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 32rpx;
  height: 16rpx;
  border: 5rpx solid white;
  border-top: none;
  border-right: none;
  transform: translate(-50%, -60%) rotate(-45deg);
  border-radius: 2rpx;
}

/* 错误图标样式 */
.error-icon-container {
  margin-bottom: 48rpx;
  animation: iconFadeIn 0.6s ease-out 0.2s both;
}

.error-icon {
  width: 128rpx;
  height: 128rpx;
  margin: 0 auto;
}

.error-mark {
  position: relative;
  width: 100%;
  height: 100%;
}

.error-circle {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  border: 6rpx solid white;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(10rpx);
  box-shadow:
    0 8rpx 32rpx rgba(255, 255, 255, 0.1),
    inset 0 2rpx 8rpx rgba(255, 255, 255, 0.2);
}

.error-circle::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 40rpx;
  height: 5rpx;
  background: white;
  transform: translate(-50%, -50%) rotate(45deg);
  border-radius: 3rpx;
}

.error-circle::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 40rpx;
  height: 5rpx;
  background: white;
  transform: translate(-50%, -50%) rotate(-45deg);
  border-radius: 3rpx;
}

.modal-title {
  color: white;
  font-weight: bold;
  margin: 0;
  font-size: 5vw;
  line-height: 1.2;
}

.modal-message {
  color: white;
  margin-bottom: 64rpx;
  font-size: 4vw;
  line-height: 1.4;
  opacity: 0.9;
  animation: textFadeIn 0.6s ease-out 0.5s both;
}

.modal-link-container {
  margin-bottom: 64rpx;
  text-align: center;
  animation: textFadeIn 0.6s ease-out 0.6s both;
}

.modal-link {
  color: white;
  font-size: 4vw;
  text-decoration: underline;
  text-underline-offset: 4rpx;
  opacity: 0.9;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.modal-link:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
}

.modal-link:active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.15);
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: auto;
  gap: 24rpx;
}

.modal-cancel-btn {
  flex: 1;
  max-width: 38vw;
}

.modal-confirm-btn {
  flex: 1;
  max-width: 38vw;
}

/* 背景装饰元素 */
.modal-bg-decoration {
  position: absolute;
  inset: 0;
  pointer-events: none;
  overflow: hidden;
  border-radius: 48rpx;
  will-change: transform;
  transform: translateZ(0); /* 启用硬件加速 */
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  will-change: transform;
  transform: translateZ(0);
}

.decoration-circle-1 {
  width: 240rpx;
  height: 240rpx;
  top: -60rpx;
  right: -60rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 70%);
}

.decoration-circle-2 {
  width: 160rpx;
  height: 160rpx;
  bottom: 20%;
  left: -40rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.03) 70%);
}

.decoration-circle-3 {
  width: 120rpx;
  height: 120rpx;
  top: 30%;
  left: 20rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.02) 70%);
}

.decoration-circle-4 {
  width: 80rpx;
  height: 80rpx;
  top: 60%;
  right: 40rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.04) 70%);
}

.decoration-wave {
  position: absolute;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.1);
  will-change: transform;
  transform: translateZ(0);
}

.decoration-wave-1 {
  width: 400rpx;
  height: 400rpx;
  top: -100rpx;
  left: -100rpx;
  transform: scale(0.8);
}

.decoration-wave-2 {
  width: 400rpx;
  height: 400rpx;
  bottom: -160rpx;
  right: -160rpx;
  transform: scale(1.2);
  border-color: rgba(255, 255, 255, 0.06);
}

.decoration-wave-3 {
  width: 300rpx;
  height: 300rpx;
  top: 40%;
  right: -60rpx;
  border-color: rgba(255, 255, 255, 0.08);
}

/* 几何图形装饰 */
.decoration-triangle {
  position: absolute;
  width: 0;
  height: 0;
}

.decoration-triangle-1 {
  top: 15%;
  right: 25%;
  border-left: 16rpx solid transparent;
  border-right: 16rpx solid transparent;
  border-bottom: 28rpx solid rgba(255, 255, 255, 0.15);
  transform: rotate(15deg);
}

.decoration-triangle-2 {
  bottom: 25%;
  left: 15%;
  border-left: 12rpx solid transparent;
  border-right: 12rpx solid transparent;
  border-bottom: 20rpx solid rgba(255, 255, 255, 0.1);
  transform: rotate(-30deg);
}

.decoration-square {
  position: absolute;
  background: rgba(255, 255, 255, 0.08);
  transform: rotate(45deg);
}

.decoration-square-1 {
  width: 24rpx;
  height: 24rpx;
  top: 25%;
  left: 20%;
}

.decoration-square-2 {
  width: 16rpx;
  height: 16rpx;
  bottom: 35%;
  right: 30%;
}

/* 星星装饰 */
.decoration-star {
  position: absolute;
  color: rgba(255, 255, 255, 0.6);
  font-size: 32rpx;
  user-select: none;
}

.decoration-star-1 {
  top: 5%;
  left: 5%;
  font-size: 40rpx;
  opacity: 0.8;
}

.decoration-star-2 {
  top: 35%;
  right: 15%;
  font-size: 28rpx;
  opacity: 0.6;
}

.decoration-star-3 {
  bottom: 20%;
  left: 30%;
  font-size: 24rpx;
  opacity: 0.7;
}

.decoration-star-4 {
  bottom: 40%;
  right: 25%;
  font-size: 36rpx;
  opacity: 0.5;
}

.decoration-star-5 {
  top: 80%;
  left: 5%;
  font-size: 20rpx;
  opacity: 0.6;
}

/* 点阵装饰 */
.decoration-dots {
  position: absolute;
  display: grid;
  gap: 12rpx;
}

.decoration-dots-1 {
  top: 5%;
  right: 5%;
  grid-template-columns: repeat(3, 1fr);
}

.decoration-dots-2 {
  bottom: 10%;
  left: 5%;
  grid-template-columns: repeat(2, 1fr);
}

.dot {
  width: 6rpx;
  height: 6rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
}

.dot:nth-child(2n) {
  opacity: 0.6;
}

.dot:nth-child(3n) {
  opacity: 0.3;
}

/* 流光效果 */
.decoration-glow {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  filter: blur(4rpx);
}

.decoration-glow-1 {
  width: 200rpx;
  height: 200rpx;
  top: 60%;
  left: 10%;
  opacity: 0.6;
}

.decoration-glow-2 {
  width: 160rpx;
  height: 160rpx;
  bottom: 30%;
  right: 20%;
  opacity: 0.4;
}

/* 网格背景 */
.decoration-grid {
  position: absolute;
  inset: 0;
  opacity: 0.03;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.1) 2rpx, transparent 2rpx),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 2rpx, transparent 2rpx);
  background-size: 40rpx 40rpx;
  background-position: -2rpx -2rpx;
}

/* 增强主背景的复杂度 */
.message-modal-content::before {
  content: '';
  position: absolute;
  inset: 0;
  background:
    radial-gradient(circle at 10% 10%, rgba(255, 255, 255, 0.1) 0%, transparent 30%),
    radial-gradient(circle at 90% 20%, rgba(91, 179, 252, 0.2) 0%, transparent 40%),
    radial-gradient(circle at 20% 90%, rgba(255, 255, 255, 0.08) 0%, transparent 35%),
    radial-gradient(circle at 80% 70%, rgba(78, 166, 248, 0.15) 0%, transparent 45%);
  pointer-events: none;
  border-radius: 48rpx;
}

/* 添加微妙的纹理效果 */
.message-modal-content::after {
  content: '';
  position: absolute;
  inset: 0;
  background: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 4rpx,
    rgba(255, 255, 255, 0.02) 4rpx,
    rgba(255, 255, 255, 0.02) 8rpx
  );
  pointer-events: none;
  border-radius: 48rpx;
}

/* 动画关键帧 */
@keyframes iconFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.5) translateY(20rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes textFadeIn {
  0% {
    opacity: 0;
    transform: translateY(15rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
