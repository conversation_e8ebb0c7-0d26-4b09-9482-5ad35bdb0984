import type { VNodeChild } from 'vue'
import type { Props } from '~/utils/compFn'
import { h } from 'vue'
import { createFuncComp, withInstall } from '~/utils/compFn'
import TheModal from './index.vue'
import MessageSlot from './MessageSlot.vue'
import TheModalFunc from './TheModalFunc.vue'

// 基础函数式组件（使用原始组件）
export const TheModalFn = createFuncComp(
  withInstall(TheModal, 'TheModal'),
)

// 扩展接口，支持插槽
interface TheModalOptions extends Props {
  width?: string
  gap?: string
  onSuccess?: (result?: any) => void
  onError?: (error?: any) => void
  onClose?: () => void
  // 插槽内容
  content?: () => VNodeChild | VNodeChild[]
  // 或者使用 slots 对象
  slots?: {
    default?: () => VNodeChild | VNodeChild[]
  }
}
// 专门用于函数式调用的组件
const TheModalFuncComp = createFuncComp<TheModalOptions>(
  withInstall(TheModalFunc, 'TheModalFunc'),
)

// 增强版函数式组件，支持插槽
export const TheModalWithSlots = (options: TheModalOptions = {}) => {
  const { content, slots, onClose, ...restOptions } = options

  // 构建插槽对象
  const modalSlots: Record<string, () => VNodeChild | VNodeChild[]> = {}

  if (content) {
    modalSlots.default = content
  }
  else if (slots?.default) {
    modalSlots.default = slots.default
  }

  return TheModalFuncComp({
    ...restOptions,
    close: onClose,
    slots: modalSlots,
  })
}

// 便捷方法：显示确认对话框
export const showConfirm = (options: {
  title?: string
  message: string
  confirmText?: string
  cancelText?: string
  showCancel?: boolean
  onConfirm?: () => void
  onCancel?: () => void
  width?: string
  type?: 'success' | 'error'
  // 超链接相关选项
  linkText?: string
  linkUrl?: string
  onLinkClick?: () => void
  slots?: {
    default?: () => VNodeChild | VNodeChild[]
  }
}) => {
  const { close } = TheModalWithSlots({
    slots: {
      default: () => h(MessageSlot, {
        ...options,
        onConfirm: () => {
          // 先执行自定义的确认回调
          options.onConfirm?.()
          // 关闭弹窗
          close()
        },
        onCancel: () => {
          // 先执行自定义的取消回调
          options.onCancel?.()
          // 关闭弹窗
          close()
        },
        onLinkClick: () => {
          // 先执行自定义的链接点击回调
          options.onLinkClick?.()
          // 打开链接
          if (options.linkUrl) {
            window.open(options.linkUrl, '_blank')
          }
          // 关闭弹窗
          close()
        },
      }),
    },
  })

  return { close }
}

// 便捷方法：显示信息对话框
export const showAlert = (options: {
  title?: string
  message: string
  buttonText?: string
  onClose?: () => void
  width?: string
}) => {
  const {
    title = '提示',
    message,
    buttonText = '确定',
    onClose,
    width = '80vw',
  } = options

  return TheModalWithSlots({
    width,
    content: () => h('div', { class: 'bg-white rounded-lg p-6 text-center' }, [
      title && h('h3', { class: 'text-lg font-semibold mb-3' }, title),
      h('p', { class: 'text-gray-600 mb-6' }, message),
      h('button', {
        class: 'px-6 py-2 bg-blue-600 text-white rounded-md',
        onClick: () => {
          onClose?.()
        },
      }, buttonText),
    ]),
  })
}
