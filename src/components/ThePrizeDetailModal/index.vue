<script setup lang="ts">
import { useModalEvents } from '@bryce-loskie/use'
import { isPrizeDetailModalVisibleRef } from '~/logic/lottery'

const { onOpen } = useModalEvents(isPrizeDetailModalVisibleRef)

const handleClose = () => {
  isPrizeDetailModalVisibleRef.value = false
}

const { data, isLoading, refetch } = useQuery({
  queryKey: ['prizeDetail'],
  queryFn: () => miscApi.getUserDrawRecord(),
  select: (data) => {
    return data?.data
  },
  enabled: false,
})

onOpen(() => {
  refetch()
})

const { data: isReceived, isLoading: isCheckingIsReceivedRef, refetch: checkIsReceived } = useQuery({
  queryKey: ['isReceived'],
  queryFn: () => miscApi.checkIsReceived(),
  select: (data) => {
    return data?.data ?? false
  },
  enabled: false,
})

onOpen(() => {
  checkIsReceived()
})

const { mutate: receivePrize, isLoading: isReceivingPrizeRef } = useMutation({
  // TODO: this api is not implemented yet
  mutationFn: () => miscApi.receivePrize(),
  onSuccess: () => {
    checkIsReceived()
    uni.showToast({
      title: '领取成功',
      icon: 'success',
    })
  },
})
</script>

<template>
  <wd-popup
    v-model="isPrizeDetailModalVisibleRef"
    safe-area-inset-bottom
    closable
    position="bottom"
    custom-class="rounded-t-32rpx"
    @close="handleClose"
  >
    <!-- Loading State -->
    <div v-if="isLoading" class="flex flex-col items-center justify-center py-20 px-6">
      <div class="i-svg-spinners-ring-resize text-3xl text-blue-500 mb-4" />
      <p class="text-gray-600 dark:text-gray-400 text-sm">
        加载抽奖信息中...
      </p>
    </div>

    <div v-else-if="data" class="h-fit overflow-auto rules-container scrollbar">
      <div class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl p-6 shadow-lg">
        <h2 class="text-lg font-bold text-gray-800 mb-6 text-center">
          🎁 奖品详情
        </h2>

        <div v-for="(prize, index) in (Array.isArray(data) ? data : [data])" :key="prize.prizeId || index" class="mb-6 last:mb-0">
          <div class="bg-white rounded-xl shadow-md overflow-hidden">
            <!-- Prize Image -->
            <div class="relative bg-gradient-to-br from-blue-500 to-purple-600 p-6">
              <div class="flex justify-center">
                <div class="relative">
                  <img
                    v-if="prize.pic"
                    :src="prize.pic"
                    :alt="prize.prizeName"
                    class="w-full h-32 object-cover rounded-xl shadow-lg ring-4 ring-white/20"
                  >
                  <div v-else class="w-32 h-32 bg-white/20 rounded-xl flex items-center justify-center">
                    <div class="i-tabler:gift text-4xl text-white" />
                  </div>

                  <!-- Status Badge -->
                  <!--                  <div class="absolute -top-2 -right-2"> -->
                  <!--                    <div -->
                  <!--                      class="px-3 py-1 rounded-full text-xs font-bold shadow-lg" -->
                  <!--                      :class="prize.enable -->
                  <!--                        ? 'bg-green-500 text-white' -->
                  <!--                        : 'bg-red-500 text-white'" -->
                  <!--                    > -->
                  <!--                      {{ prize.enable ? '可用' : '不可用' }} -->
                  <!--                    </div> -->
                  <!--                  </div> -->
                </div>
              </div>
            </div>

            <!-- Prize Info -->
            <div class="p-6">
              <!-- Prize Name -->
              <h3 class="text-lg font-bold text-gray-800 mb-4 leading-relaxed">
                {{ prize.prizeName?.replace(/\\n/g, '\n') }}
              </h3>

              <!-- Additional Info -->
              <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                <div class="flex items-center gap-2">
                  <div class="i-tabler:info-circle text-blue-500 shrink-0" />
                  <span class="text-sm text-gray-600">领取信息</span>
                </div>
                <div class="flex items-center gap-2">
                  <div
                    class="w-2 h-2 rounded-full"
                    :class="isReceived ? 'bg-green-500' : 'bg-red-500'"
                  />
                  <span class="text-sm font-medium" :class="isReceived ? 'text-green-600' : 'text-red-600'">
                    {{ isReceived ? '已领取' : '待领取' }}
                  </span>
                </div>
              </div>

              <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                <div class="flex items-center gap-2">
                  <div class="i-tabler:info-circle text-blue-500 shrink-0" />
                  <span class="text-sm text-gray-600">领取时间</span>
                </div>
                <div class="flex items-center gap-2">
                  <span class="text-sm font-medium" :class="isReceived ? 'text-green-600' : 'text-red-600'">
                    15:00 - 17:00
                  </span>
                </div>
              </div>

              <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                <div class="flex items-center gap-2">
                  <div class="i-tabler:info-circle text-blue-500 shrink-0" />
                  <span class="text-sm text-gray-600">领取地点</span>
                </div>
                <div class="flex items-center gap-2">
                  <span class="text-sm font-medium" :class="isReceived ? 'text-green-600' : 'text-red-600'">
                    魔法海洋入口处
                  </span>
                </div>
              </div>

              <!-- 领取按钮 -->
              <wd-button
                v-if="!isReceived"
                class="w-full mt-6"
                :loading="isCheckingIsReceivedRef || isReceivingPrizeRef"
                :disabled="!prize.enable || isReceived"
                @click="receivePrize()"
              >
                已领取
              </wd-button>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="!data || (Array.isArray(data) && data.length === 0)" class="text-center py-12">
          <div class="i-tabler:gift-off text-6xl text-gray-400 mb-4" />
          <h3 class="text-lg font-medium text-gray-600 mb-2">
            暂无奖品信息
          </h3>
          <p class="text-sm text-gray-500">
            请稍后再试
          </p>
        </div>
      </div>
    </div>

    <div v-else class="fc py-20 px-6">
      <p class="text-gray-600 dark:text-gray-400 text-sm">
        暂无抽奖信息
      </p>
    </div>
  </wd-popup>
</template>
