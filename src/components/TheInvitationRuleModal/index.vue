<script setup lang="ts">
import { isInvitationRuleModalVisibleRef } from '~/logic/task'

const isLoading = ref(false)

const handleClose = () => {
  isInvitationRuleModalVisibleRef.value = false
}
</script>

<template>
  <wd-popup
    v-model="isInvitationRuleModalVisibleRef"
    safe-area-inset-bottom
    closable
    position="bottom"
    custom-class="rounded-t-32rpx"
    @close="handleClose"
  >
    <!-- Loading State -->
    <div v-if="isLoading" class="flex flex-col items-center justify-center py-20 px-6">
      <div class="i-svg-spinners-ring-resize text-3xl text-blue-500 mb-4" />
      <p class="text-gray-600 dark:text-gray-400 text-sm">
        加载规则中...
      </p>
    </div>

    <div class="p-4 h-80vh overflow-auto rules-container scrollbar">
      <div class="text-pretty prose" style="font-size: 11px">
        <p style="font-size: 14px;">
          <b>线下探险活动规则</b>
        </p>
        一、总则 <br>
        1.1 参与条件 <br>
        新入园游客须添加天津极地海洋度假区或酒店前台扫码添加官方企业微信，获取电子任务卡及探险地图。 <br>
        以前添加官方企业微信客服的游客，可发送关键词“探险”，获得电子任务卡。 <br>
        1.2 任务挑战规则 <br>
        1.2.1 任务内容 <br>
        游客需在园区内指定区域完成相应官方设定任务，即可获得奖品，具体任务以现场公示为准。 <br>
        - 前往园区内 7 大场馆（企鹅馆、白鲸馆、鲸豚剧场等），通过电子地图查看任务详情（如拍照打卡、科普答题、等）； <br>
        - 完成任务后上传凭证或确认互动，系统自动点亮关卡站点，获取对应的 “龙宫能量石”。 <br>
        1.2.2 任务时效 <br>
        活动时间为当日有效，活动打卡数据当晚24点清零，未完成视为自动放弃挑战资格。 <br>
        1.3 奖励兑换规则 <br>
        1.3.1 兑换条件 <br>
        获取 4 颗能量石可兑换小海豚精美玩偶一个，获取 7 颗可解锁「探险家挑战通关认证」并参与分享抽大奖，最高可获得价值998元的亲子年卡。 <br>
        1.3.2 兑换限制 <br>
        奖品每日限量供应，兑完即止；不可折现、不可叠加兑换。 <br>
        1.3.3 兑换时效 <br>
        须在活动期内兑换，活动结束后未兑换自动失效。 <br>
        二、分享抽奖活动规则 <br>
        2.1参与条件 <br>
        - 参与活动用户需为当日入园，且成功完成7个挑战关卡点用户； <br>
        - 发布#天津极地嗨浪节 话题分享活动至任意社交平台（如微信朋友圈、小红书、抖音、快手等）后，返回活动页面自动弹出抽奖大转盘。 <br>
        2.3抽奖规则 <br>
        每位用户活动期间仅限参与 1 次，采用系统随机抽奖方式，具体奖品明细如下： <br>
        <table>
          <thead>
            <tr>
              <th>奖品名称</th>
              <th style="width: 40%">
                奖品图片
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style="font-size: 11px">
                龙宫亲子通行证 · 1大1小亲子年卡<br> 一票玩乐所有场馆，全年不限次入园！
              </td>
              <td><img src="https://tjlg.haiheangroup.com/gift/1.png" alt=""></td>
            </tr>
            <tr>
              <td style="font-size: 11px">
                探险队专属战袍 · IP款儿童短袖T恤<br> 百搭吸睛，穿上这件衣服，你就是龙宫官方认证的小勇士！
              </td>
              <td><img src="https://tjlg.haiheangroup.com/gift/2.png" alt=""></td>
            </tr>
            <tr>
              <td style="font-size: 11px">
                龙宫守护兽 · 5寸美系毛绒玩具<br> 软萌好抱，带回你最喜欢的能量伙伴~
              </td>
              <td><img src="https://tjlg.haiheangroup.com/gift/3.png" alt=""></td>
            </tr>
            <tr>
              <td style="font-size: 11px">
                巴蒂魔法头环 · IP款巴蒂闪光发箍<br> 巴蒂同款触角发箍，戴上秒变公主，还会发光哦！
              </td>
              <td><img src="https://tjlg.haiheangroup.com/gift/4.png" alt=""></td>
            </tr>
            <tr>
              <td style="font-size: 11px">
                冒险神器 · 奶龙晶蓝可擦中性笔（单支装）<br> 记录学习点滴，错了也能擦！
              </td>
              <td><img src="https://tjlg.haiheangroup.com/gift/5.png" alt=""></td>
            </tr>
            <tr>
              <td style="font-size: 11px">
                龙宫幻影召唤卡 · IP主题动物光栅卡<br> 一晃秒变3D动画，立体切换，玩趣十足超炫酷！
              </td>
              <td><img src="https://tjlg.haiheangroup.com/gift/6.png" alt=""></td>
            </tr>
          </tbody>
        </table>
        ******* 奖品兑换与发放规则<br>
        - 实物奖品需15点-17点至【魔法海洋入口处】核销领取，中奖者需按要求向工作人员出示中奖奖品，点击“我的奖品”页面。具体规则详见《天津极地海洋度假区 2025 年 “龙宫宝藏打卡之旅” 抽奖活动规则》。<br>
        - 奖品领取完成后需在工作人员指导下点击“已领取”图标，请勿随意点击！由个人点击造成的奖品页显示已领取，则无法兑奖。<br>
        三、免责条款<br>
        3.1 因不可抗力（自然灾害、政策调整等）或系统故障导致活动无法正常开展，主办方有权暂停或终止活动，不承担赔偿责任。<br>
        3.2 游客在活动期间应遵守园区《游客须知》及相关法律法规，因个人过错、违法行为导致的人身财产损失，由游客自行承担。<br>
        3.3 如发现游客存在作弊、欺诈等违规行为，主办方有权取消其获奖资格，并保留追究法律责任的权利。<br>
        四、附则<br>
        4.1 违规处理<br>
        参与者若存在刷赞、盗用他人内容等作弊行为，一经核实，主办方将取消其获奖资格、追回已发奖品，并保留追究法律责任的权利。<br>
        4.2 法律声明<br>
        分享内容需符合《网络安全法》等法规，不得含侵权、低俗或违法信息。因内容违规导致主办方损失的，参与者需承担全部责任并赔偿损失。<br>
        4.3 规则变更<br>
        主办方可根据实际情况调整活动规则，调整内容将通过园区公告、官方企微及公众号公示，公示后用户继续参与即视为接受新规则。<br>
        4.4 咨询方式<br>
        客服热线：022-66227777（每日 9:00-16:30）；或通过企业微信 “天津极地海洋度假区小助手” 咨询。
      </div>
    </div>
  </wd-popup>
</template>

<style scoped>
/* Enhance readability of HTML content */
.rules-content :deep(h1),
.rules-content :deep(h2),
.rules-content :deep(h3),
.rules-content :deep(h4),
.rules-content :deep(h5),
.rules-content :deep(h6) {
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  line-height: 1.4;
}

.rules-content :deep(h1) {
  font-size: 1.5rem;
  border-bottom: 2px solid rgb(59 130 246);
  padding-bottom: 0.5rem;
}

.rules-content :deep(h2) {
  font-size: 1.25rem;
  color: rgb(59 130 246);
}

.rules-content :deep(p) {
  margin-bottom: 1rem;
  line-height: 1.7;
}

.rules-content :deep(ul),
.rules-content :deep(ol) {
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.rules-content :deep(li) {
  margin: 0.5rem 0;
  line-height: 1.6;
}

.rules-content :deep(strong),
.rules-content :deep(b) {
  font-weight: 600;
  color: rgb(17 24 39);
}

.dark .rules-content :deep(strong),
.dark .rules-content :deep(b) {
  color: rgb(249 250 251);
}

.rules-content :deep(em),
.rules-content :deep(i) {
  font-style: italic;
  color: rgb(75 85 99);
}

.dark .rules-content :deep(em),
.dark .rules-content :deep(i) {
  color: rgb(156 163 175);
}

.rules-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
  border: 1px solid rgb(229 231 235);
  border-radius: 0.5rem;
  overflow: hidden;
}

.dark .rules-content :deep(table) {
  border-color: rgb(75 85 99);
}

.rules-content :deep(th),
.rules-content :deep(td) {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid rgb(229 231 235);
}

.dark .rules-content :deep(th),
.dark .rules-content :deep(td) {
  border-bottom-color: rgb(75 85 99);
}

.rules-content :deep(th) {
  background-color: rgb(249 250 251);
  font-weight: 600;
}

.dark .rules-content :deep(th) {
  background-color: rgb(55 65 81);
}
</style>
