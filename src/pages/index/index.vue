<script setup lang="ts">
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import * as WX from 'jswx'
import { find } from 'lodash-es'
import { motion } from 'motion-v'
import { LotteryStatusEnum } from '~/api/misc'

import bg from '~/assets/images/bg.png?url'
import iconBack from '~/assets/images/icon_back.png?url'
import logo from '~/assets/images/logo.png?url'
import moreActivities from '~/assets/images/stages/查看表演时间.png?url'
import StageArrowsIcon from '~/assets/images/stages/箭头.png?url'
import stageLineImageUrl from '~/assets/images/stages/线路.png?url'
import lotteryIcon from '~/assets/images/抽奖.png?url'

import heroTextIcon from '~/assets/images/文字.png?url'
import energyStone from '~/assets/images/能量石.png?url'

import hotelIcon from '~/assets/images/酒店.png?url'

import { showConfirm } from '~/components/TheModal'
import { isLotteryModalVisibleRef, isPrizeDetailModalVisibleRef } from '~/logic/lottery'
import { currentMissionIdRef, isMissionModalVisibleRef, stageListRef, StateEnum, useStageList } from '~/logic/stage'

import { isRulesModalVisibleRef, isShareModalVisibleRef, isTaskCompleteModalVisibleRef, isTaskStartModalVisibleRef } from '~/logic/task'

const allImages: string[] = [
  bg,
  heroTextIcon,
  stageLineImageUrl,
  StageArrowsIcon,
  hotelIcon,
  lotteryIcon,
  energyStone,
  moreActivities,
  ...stageListRef.value.map(stage => stage.icon),
]

// 箭头图标位置配置 - 沿着线路从下到上分布
const arrowPositions = [
  { bottom: '5%', left: '34%', transform: 'rotate(210deg)' }, // 第1个箭头 - 入口附近
  { bottom: '25%', right: '39%' }, // 第2个箭头 - 企鹅馆到白鲸馆之间
  { bottom: '42%', right: '15%', transform: 'rotate(210deg)' }, // 第3个箭头 - 向上
  { bottom: '48.5%', left: '33%', transform: 'rotate(160deg)' }, // 第4个箭头 - 亚特兰蒂斯通道附近
  { bottom: '65%', left: '0%', transform: 'rotate(280deg)' }, // 第5个箭头 - 水母馆附近
  { bottom: '74%', right: '40%', transform: 'rotate(20deg)' }, // 第6个箭头 - 继续向上
  { bottom: '86%', right: '31%', transform: 'rotate(210deg)' }, // 第7个箭头 - 海洋嘉年华附近
]

const isReadyRef = ref(false)
const init = async () => {
  const loadImage = (image: string) => {
    return new Promise((resolve) => {
      const img = new Image()
      img.src = image
      img.onload = () => {
        resolve(true)
      }
      img.onerror = () => {
        resolve(false)
      }
    })
  }
  // preload images
  await Promise.allSettled(allImages.map(loadImage))
  isReadyRef.value = true
}
init()

const modalBgImageRef = ref<HTMLDivElement>()
const modalBgImageHeightRef = ref(0)
watchEffect(() => {
  modalBgImageHeightRef.value = modalBgImageRef.value?.clientHeight ?? 0
})

const handleViewRules = () => {
  isRulesModalVisibleRef.value = true
}

const userStore = useUserStore()

const queryClient = useQueryClient()

// 处理 m === '4' 的情况，直接完成任务并进入下一关
const { mutate: handleAutoComplete } = useMutation({
  mutationFn: (missionId: string) => miscApi.completeMission(missionId),
  onSuccess: (_, variables) => {
    if (variables === StateEnum.Stage7) {
      queryClient.invalidateQueries({ queryKey: miscApi.getUserMissionListQueryKey })
      return
    }
    isTaskCompleteModalVisibleRef.value = true
    queryClient.invalidateQueries({ queryKey: miscApi.getUserMissionListQueryKey })
    // 清除 m 参数
    userStore.m = undefined
  },
  onError: (error: any) => {
    console.error('Auto complete stage 4 failed:', error)
    uni.showToast({
      title: error?.message ?? '请稍后重试',
      icon: 'error',
    })
  },
})
const message = ref('\n\n获得4颗或7颗能量石\n可前往线下指定区域兑换好礼')

const { totalEnergy } = useStageList({
  onSuccess: () => {
    const m = userStore.m
    if (m && m === '3') {
      currentMissionIdRef.value = '3'
      handleAutoComplete('3')
    }
    else if (m) {
      currentMissionIdRef.value = m
      isMissionModalVisibleRef.value = true
    }
    else {
      // 从存储中读取有没有点击开始
      const hasStarted = localStorage.getItem('hasStarted')
      if (hasStarted !== 'true') {
        isTaskStartModalVisibleRef.value = true
      }
    }
  },
})

const currentPendingStageIdRef = computed(() => {
  for (let idx = 0; idx < stageListRef.value.length; idx++) {
    const stage = stageListRef.value[idx]
    if (!stage?.hasFinished)
      return stage.id
  }
  return null
})

const handleShare = () => {
  isShareModalVisibleRef.value = true
}

const handleHotelIconClick = () => {
  const { close } = showConfirm({
    message: '恭喜您获得专属美人鱼卡片\n请于酒店前台领取',
    confirmText: '好的',
    type: 'success',
    onConfirm: () => {
      // do nothing
      close()
    },
  })
}

// 抽奖
const handleLottery = async () => {
  uni.showLoading({ title: '加载中...' })
  const [err, res] = await to(miscApi.checkLotteryStatus())
  uni.hideLoading()
  if (err) {
    uni.showToast({
      title: err.message ?? '获取抽奖状态失败, 请稍后重试',
      icon: 'error',
    })
    return
  }

  const status = res?.data

  // if status is null, means user has not lottery yet and has ability to lottery
  if (!status) {
    isLotteryModalVisibleRef.value = true
    return
  }

  if (status === LotteryStatusEnum.AlreadyLottery) {
    const { close } = showConfirm({
      title: '温馨提示',
      message: '您已抽奖过, 点击查看抽奖记录～',
      confirmText: '查看抽奖记录',
      cancelText: '取消',
      showCancel: false,
      onConfirm: () => {
        isPrizeDetailModalVisibleRef.value = true
        close()
      },
    })
    return
  }

  if (status === LotteryStatusEnum.NotEnough) {
    uni.showModal({
      title: '温馨提示',
      content: '需要任务全部完成后才能抽奖',
      showCancel: false,
      confirmText: '我知道啦',
      onConfirm: () => {
        close()
      },
    })
    return
  }

  isLotteryModalVisibleRef.value = true
}

// 能量石
const handleEnergyStone = async () => {
  if (totalEnergy.value === 4 || totalEnergy.value === 7) {
    message.value = '您已获得好礼兑换资格\n\n请前往线下指定位置进行兑换'
  }
  const { close } = showConfirm({
    title: '我的能量石',
    message: `当前能量石: ${totalEnergy.value} 颗\n\n ${message.value}`,
    confirmText: '好的',
    onConfirm: () => {
      // do nothing
      close()
    },
  })
}

// 跳转表演时间表
const handleMoreActivities = () => {
  // @ts-ignore
  WX.miniProgram.redirectTo({
    url: '/package_park/pages/function/list?parkId=4&functionId=2&functionName=鲸奇海洋秀',
  })
}

// 拍照任务完成
const handleCompleteTakePhoto = () => {
  if (find(stageListRef.value, { id: StateEnum.Stage7 })?.hasFinished)
    return
  handleAutoComplete(StateEnum.Stage7)
}
</script>

<template>
  <main
    v-if="isReadyRef"
    class="relative max-w-70ch mx-auto w-screen h-screen bg-cover bg-center bg-no-repeat pt-1.5vh"
    :style="{ backgroundImage: `url(${bg})` }"
  >
    <!-- Top Header -->
    <img
      :src="logo"
      alt="Logo"
      class="h-3vh w-auto absolute left-6%"
    >

    <!-- Main Title -->
    <div class="absolute top-6vh left-5.47% z-10">
      <motion.img
        :src="heroTextIcon"
        alt="极地探险家"
        class="h-auto w-51.2vw max-w-220px"
        :while-in-view="{ scaleX: 1 }"
        :initial="{ scaleX: 0 }"
      />
    </div>

    <!-- Interactive Areas, stage line -->
    <section class="relative inset-x-0 z-5 fc top-14vh">
      <motion.img
        :src="stageLineImageUrl"
        alt="Stage Line"
        class="w-84vw h-auto"
        :while-in-view="{
          scale: 1,
          opacity: 1,
        }"
        :initial="{
          scale: 0.5,
          opacity: 0,
        }"
        :transition="{
          delay: 0.2,
          duration: 0.3,
        }"
      />

      <!-- Stage Arrows - 沿着线路的箭头动画 -->
      <div class="absolute w-full h-full inset-x-0 inset-y-0 z-4 scale-y-94 scale-x-90 translate-x-4">
        <motion.div
          v-for="(position, idx) in arrowPositions"
          :key="`arrow-${idx}`"
          class="absolute pointer-events-none"
          :style="position as any"
          :initial="{ opacity: 0 }"
          :while-in-view="{ opacity: 1 }"
          :transition="{ delay: 0.5 + idx * 0.1, duration: 0.3 }"
        >
          <img
            :src="StageArrowsIcon"
            alt="箭头"
            class="w-auto h-1.8vh arrow-blink"
            :style="{ animationDelay: `${2 + idx * 0.3}s` }"
            draggable="false"
          >
        </motion.div>
      </div>

      <!-- Carnival Area -->
      <ul class="absolute w-full h-full inset-x-0 inset-y-0 z-5 scale-y-94 scale-x-90 translate-x-4">
        <motion.li
          v-for="stage, idx in stageListRef"
          :key="stage.id"
          class="absolute cursor-pointer"
          :class="[(stage.hasFinished || stage.id === currentPendingStageIdRef) ? '' : 'grayscale', stage.id === currentPendingStageIdRef ? 'animate-it' : '']"
          :style="stage.position as any"
          :while-in-view="{
            scale: 1,
            opacity: 1,
          }"
          :initial="{
            scale: 1.5,
            opacity: 0,
          }"
          :transition="{
            delay: 0.3 + idx * 0.06,
            duration: 0.3,
          }"
          @click="(stage.hasFinished || stage.id === currentPendingStageIdRef) ? stage.onClick?.(stage) : null"
        >
          <motion.img
            :src="stage.icon"
            :alt="stage.name"
            class="w-auto"
            :class="[stage.hasFinished ? '' : 'cursor-not-allowed']"
            :style="{ height: stage.height }"
            draggable="false"
            :transition="{ duration: 0.3 }"
            :while-hover="stage.hasFinished ? { scale: 1.1 } : {}"
            :while-press="stage.hasFinished ? { scale: 0.9 } : {}"
          />
        </motion.li>
      </ul>
    </section>

    <!-- Hotel (bottom left) -->
    <motion.div
      class="absolute cursor-pointer w-14.13vw left-8.93vw top-62vh animate-it"
      :while-hover="{ scale: 1.1 }"
      :while-press="{ scale: 0.9 }"
      :transition="{ x: { delay: 0.9 }, opacity: { delay: 0.9 }, duration: 0.3 }"
      :initial="{ x: '-5vw', opacity: 0 }"
      :while-in-view="{ x: 0, opacity: 1 }"
      @click="handleHotelIconClick"
    >
      <img
        :src="hotelIcon"
        alt="酒店"
        class="w-full h-auto transition-all duration-300"
      >
    </motion.div>

    <!-- Footer -->
    <footer class="w-full inset-x-0 bottom-0 absolute min-h-22vh">
      <div class="w-full h-full relative ">
        <div class="flex items-center justify-between px-8.8vw">
          <ButtonBlue enable-slide-up-animation @click="handleShare">
            邀请购票
          </ButtonBlue>
          <ButtonBlue enable-slide-up-animation @click="handleEnergyStone">
            能量石 {{ totalEnergy }} 个
          </ButtonBlue>
        </div>
        <div class="px-8.8vw w-full fc mt-2">
          <motion.div
            :while-hover="{ scale: 1.1 }"
            :while-press="{ scale: 0.9 }"
            class="w-full h-auto"
            :transition="{ y: { delay: 1.2 }, opacity: { delay: 1.2 }, duration: 0.3 }"
            :initial="{ y: '2rem', opacity: 0 }"
            :while-in-view="{ y: 0, opacity: 1 }"
            @click="handleLottery"
          >
            <img :src="lotteryIcon" alt="抽奖" class="w-full h-auto">
          </motion.div>
        </div>

        <div class="pl-20.08vw w-full mt-2">
          <motion.div
            :while-hover="{ scale: 1.1 }"
            :while-press="{ scale: 0.9 }"
            class="w-47.07vw h-auto"
            :transition="{ y: { delay: 1.35 }, opacity: { delay: 1.35 }, duration: 0.3 }"
            :initial="{ y: '2rem', opacity: 0 }"
            :while-in-view="{ y: 0, opacity: 1 }"
            @click="handleMoreActivities"
          >
            <img :src="moreActivities" alt="周末还有更多活动" class="w-full h-auto">
          </motion.div>
        </div>

        <motion.div
          class="absolute right-3.06vw bottom-1.77vh"
          :while-hover="{ scale: 1.1 }"
          :while-press="{ scale: 0.9 }"
          :transition="{ x: { delay: 1.5 }, opacity: { delay: 1.5 }, duration: 0.3 }"
          :initial="{ x: '10vw', opacity: 0 }"
          :while-in-view="{ x: 0, opacity: 1 }"
          @click="handleViewRules"
        >
          <img :src="energyStone" alt="规则" class="w-16.4vw aspect-square">
          <div class="font-hand clip-text text-white text-xs absolute left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2 w-full text-center">
            规则
          </div>
        </motion.div>
      </div>
    </footer>

    <!-- back -->
    <!-- temporary hide, because wechat mini program webview has default back button -->
    <div v-if="0" class="absolute left-[calc(3.47%-0.5rem)] top-[calc(3vh-0.5rem)] wh-36px p-2 z-11 fc aspect-square rounded-full active:bg-white/20 transition-all duration-300 cursor-pointer" @click="handleNavigateBack()">
      <img :src="iconBack" alt="back" class="h-2.09vh w-auto">
    </div>

    <!-- task start modal -->
    <TheTaskStartModal />

    <!-- task complete modal -->
    <TheTaskCompleteModal />

    <!-- take photo modal -->
    <TheTakePhotoModal @complete-take-photo="handleCompleteTakePhoto" />

    <!-- rules modal -->
    <TheRuleModal />

    <!-- mission modal -->
    <TheMissionModal />

    <!-- share modal -->
    <TheModalShare />

    <!-- lottery modal -->
    <TheLotteryModal />

    <!-- prize detail modal -->
    <ThePrizeDetailModal />

    <!-- share rules modal -->
    <TheShareRuleModal />

    <TheInvitationRuleModal />
  </main>

  <section v-else class="fc wh-screen">
    <div class="i-svg-spinners-ring-resize text-lg" />
  </section>
</template>

<style>
@keyframes tada {
  0% {
    transform: scale(1);
  }
  10%,
  20% {
    transform: scale(0.98) rotate(-2deg);
  }
  30%,
  50%,
  70%,
  90% {
    transform: scale(1.02) rotate(2deg);
  }
  40%,
  60%,
  80% {
    transform: scale(1.02) rotate(-2deg);
  }
  100% {
    transform: scale(1);
  }
}

.animate-it {
  animation: tada 1.2s ease-in-out infinite;
}

/* 箭头闪烁动画 */
.arrow-blink {
  opacity: 0.5;
  transform: scale(1);
  animation: arrow-blink-cycle 3.5s ease-in-out infinite;
}

@keyframes arrow-blink-cycle {
  0%,
  85.7% {
    opacity: 0.5;
    filter: none;
    transform: scale(1);
  }
  14.3% {
    opacity: 1;
    filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.8)) drop-shadow(0 0 16px rgba(255, 215, 0, 0.6))
      drop-shadow(0 0 24px rgba(255, 215, 0, 0.4));
    transform: scale(1.3);
  }
}
</style>
