import type { IUser } from '~/api/user'
import queryString from 'query-string'
import { parseQuery } from 'ufo'

interface IUserStoreState {
  token?: string
  userInfo?: IUser
  m?: string
  from?: 'scan' | 'share'
}

let isLoginLoading = false

export const useUserStore = defineStore('user', {
  state(): IUserStoreState {
    return {
      token: undefined,
      userInfo: undefined,
      m: undefined,
    }
  },
  actions: {
    setToken(token?: string) {
      this.token = token
    },
    setUserInfo(userInfo?: IUser) {
      this.userInfo = userInfo
    },
    logout() {
      this.token = undefined
      this.userInfo = undefined
      this.m = undefined
      this.from = undefined
    },
    async tryLogin() {
      if (isLoginLoading) {
        return
      }

      isLoginLoading = true

      const url = new URL(window.location.href.replace(/\/?#\/?/, '/'))
      const query = parseQuery(url.search)
      const openId = query.openId as string || 'fake-open-id'

      const m = query.m
      if (m)
        this.m = m as string

      const from = query.type
      if (from) {
        this.from = from as 'scan' | 'share'
        const tid = query.tid
        if (tid) {
          miscApi.shareFriend()
          window.location.href = `https://d.weimob.com/d/1_11_u2ozfe`
          return
        }
      }

      if (!openId) {
        isLoginLoading = false
        return
      }

      const [err, res] = await to(userApi.login({ openId }))
      isLoginLoading = false

      if (err)
        throw err

      const data = res?.data
      if (!data)
        throw new Error('登录失败，请重试')

      this.setToken(data.token)
      this.setUserInfo(data.userInfo)
      return res
    },
  },
  getters: {
    hasLoggedIn(state) {
      return !!state.token
    },
    isFromShare(state) {
      return state.from === 'share'
    },
    isFromScan(state) {
      return state.from === 'scan'
    },
  },
  persist: {
    enabled: true,
  },
})
